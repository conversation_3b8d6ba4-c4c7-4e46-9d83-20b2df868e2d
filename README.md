# 🏰 Dungeon Blitz - Flash Reboot

### Reviving the original Dungeon Blitz experience for archival and preservation purposes.

---

## ⚡ How to Play

1. **Install Requirements** (listed below)
2. **Run** `server.py` (don't forget to cd into server first)
3. Choose how you'd like to play:

   * **Option 1:** Flash Projector

     * Open the projector
     * Go to `File` > `Open` > Paste this URL:
       `http://localhost/p/cbv/DungeonBlitz.swf?fv=cbq&gv=cbv`
   * **Option 2:** Flash-Compatible Browser

     * Open `http://localhost/index.html` in your browser

---

## 🪠 Requirements

1. This repository (game files)
2. [Python](https://www.python.org/)
3. A Flash-compatible browser **OR** a standalone Flash projector

---

## \:flashlight: Flash Options

### Option 1: Flash Projector

* Download from [this GitHub archive](https://github.com/Grubsic/Adobe-Flash-Player-Debug-Downloads-Archive)

### Option 2: Flash-Compatible Browser

* [Flash Browser](https://github.com/radubirsan/FlashBrowser) — Open-source project with built-in Flash support

#### Flash Player Installers:

* **For Firefox / Basilisk (NPAPI):**
  [Download NPAPI Flash Player](https://archive.org/download/flashplayerarchive/pub/flashplayer/installers/archive/fp_32.0.0.371_archive.zip/32_0_r0_371%2Fflashplayer32_0r0_371_win.exe)

* **For Opera / Chromium (PPAPI):**
  [Download PPAPI Flash Player](https://archive.org/download/flashplayerarchive/pub/flashplayer/installers/archive/fp_32.0.0.371_archive.zip/32_0_r0_371%2Fflashplayer32_0r0_371_winpep.exe)

> These installers are for version **32.0.0.371**, the last build without Adobe's end-of-life (EOL) kill switch. All files come from the [Adobe Flash Player Archive](https://archive.org/download/flashplayerarchive/).

---

## \:mag: Flash-Supported Browsers

* **[Chromium 82.0](https://chromium.en.uptodown.com/windows/download/2181158)**
* **[Firefox 84.0 (64-bit)](https://download-installer.cdn.mozilla.net/pub/firefox/releases/84.0/win64/en-US/Firefox%20Setup%2084.0.exe)** or [32-bit](https://download-installer.cdn.mozilla.net/pub/firefox/releases/84.0/win32/en-US/Firefox%20Setup%2084.0.exe)
* **[Basilisk Browser](https://www.basilisk-browser.org/)** (NPAPI compatible)

> ⚠️ Firefox will auto-update by default. Disable this in:
> `Menu (三) > Options > General > Firefox Updates`

> ⚠️ Chromium disables Flash by default. You must manually enable it in settings.

---

## 📜 Legal Notice

This project is for **archival** and **educational purposes only**. All assets remain the property of their original creators. No monetization, redistribution, or alteration of copyrighted material.

If you are a rights holder and wish this project removed, please open an issue.

---
