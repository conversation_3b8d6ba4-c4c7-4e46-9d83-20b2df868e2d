#mission.py
import xml.etree.ElementTree as ET

class MissionDef:
    def __init__(self, var_1775: bool, var_908: int, var_134: bool):
        self.var_1775 = var_1775
        self.var_908   = var_908
        self.var_134   = var_134

MISSION_DEFS = [
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=False, var_908=5, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=5, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=15, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=3, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=5, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=15, var_134=False),
    MissionDef(var_1775=False, var_908=15, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=250, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=15, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=False, var_908=60, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=3, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=40, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=60, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=40, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=3, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=10, var_134=False),
    MissionDef(var_1775=False, var_908=15, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=3, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=False, var_908=40, var_134=False),
    MissionDef(var_1775=False, var_908=20, var_134=False),
    MissionDef(var_1775=False, var_908=30, var_134=False),
    MissionDef(var_1775=False, var_908=40, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=0, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
    MissionDef(var_1775=True, var_908=1, var_134=False),
]
var_238 = MISSION_DEFS

def load_mission_defs(path: str):
    tree = ET.parse(path)
    root = tree.getroot()
    defs = [None]  # index 0 unused
    for node in root.findall('MissionType'):
        mid   = int(node.findtext('MissionID', '0'))
        ctr   = int(node.findtext('CompleteCount', '0'))
        timed = node.find('Timed') is not None
        oneshot = (ctr <= 1)
        # ensure list grows to mid
        while len(defs) <= mid:
            defs.append(None)
        defs[mid] = MissionDef(var_1775=oneshot, var_908=ctr, var_134=timed)
    # fill any gaps up to the max ID with dummy
    max_id = len(defs) - 1
    for i in range(1, max_id+1):
        if defs[i] is None:
            defs[i] = MissionDef(False, 1, False)
    return defs