
default_learned_abilities = {
    "paladin": [
        {
            "abilityID": 27,
            "rank": 10
        },
        {
            "abilityID": 20,
            "rank": 9
        },
        {
            "abilityID": 21,
            "rank": 1
        },
        {
            "abilityID": 81,
            "rank": 8
        },
        {
            "abilityID": 22,
            "rank": 1
        },
        {
            "abilityID": 23,
            "rank": 1
        },
        {
            "abilityID": 19,
            "rank": 1
        },
        {
            "abilityID": 24,
            "rank": 1
        },
        {
            "abilityID": 25,
            "rank": 1
        },
        {
            "abilityID": 26,
            "rank": 1
        },
        {
            "abilityID": 48,
            "rank": 1
        },
        {
            "abilityID": 49,
            "rank": 1
        },
        {
            "abilityID": 50,
            "rank": 1
        },
        {
            "abilityID": 51,
            "rank": 1
        },
        {
            "abilityID": 52,
            "rank": 1
        },
        {
            "abilityID": 53,
            "rank": 1
        },
        {
            "abilityID": 54,
            "rank": 1
        },
        {
            "abilityID": 55,
            "rank": 1
        },
        {
            "abilityID": 56,
            "rank": 1
        },
        {
            "abilityID": 57,
            "rank": 1
        },
        {
            "abilityID": 78,
            "rank": 1
        },
        {
            "abilityID": 79,
            "rank": 1
        },
        {
            "abilityID": 80,
            "rank": 1
        },
        {
            "abilityID": 82,
            "rank": 1
        },
        {
            "abilityID": 83,
            "rank": 1
        },
        {
            "abilityID": 84,
            "rank": 1
        },
        {
            "abilityID": 85,
            "rank": 1
        },
        {
            "abilityID": 86,
            "rank": 1
        },
        {
            "abilityID": 87,
            "rank": 1
        },
        {
            "abilityID": 108,
            "rank": 1
        },
        {
            "abilityID": 109,
            "rank": 1
        },
        {
            "abilityID": 110,
            "rank": 1
        },
        {
            "abilityID": 111,
            "rank": 1
        },
        {
            "abilityID": 112,
            "rank": 1
        },
        {
            "abilityID": 113,
            "rank": 1
        },
        {
            "abilityID": 114,
            "rank": 1
        },
        {
            "abilityID": 115,
            "rank": 1
        },
        {
            "abilityID": 116,
            "rank": 1
        },
        {
            "abilityID": 117,
            "rank": 1
        }
    ],
    "rogue": [

        {
            "abilityID": 1,
            "rank": 10
        },
        {
            "abilityID": 2,
            "rank": 10
        },
        {
            "abilityID": 3,
            "rank": 10
        },
        {
            "abilityID": 4,
            "rank": 10
        },
        {
            "abilityID": 5,
            "rank": 10
        },
        {
            "abilityID": 6,
            "rank": 10
        },
        {
            "abilityID": 7,
            "rank": 10
        },
        {
            "abilityID": 8,
            "rank": 10
        },
        {
            "abilityID": 9,
            "rank": 10
        },
        {
            "abilityID": 38,
            "rank": 10
        },
        {
            "abilityID": 39,
            "rank": 10
        },
        {
            "abilityID": 40,
            "rank": 10
        },
        {
            "abilityID": 41,
            "rank": 10
        },
        {
            "abilityID": 42,
            "rank": 10
        },
        {
            "abilityID": 43,
            "rank": 10
        },
        {
            "abilityID": 44,
            "rank": 10
        },
        {
            "abilityID": 45,
            "rank": 10
        },
        {
            "abilityID": 46,
            "rank": 10
        },
        {
            "abilityID": 47,
            "rank": 10
        },
        {
            "abilityID": 68,
            "rank": 10
        },
        {
            "abilityID": 69,
            "rank": 10
        },
        {
            "abilityID": 70,
            "rank": 10
        },
        {
            "abilityID": 71,
            "rank": 10
        },
        {
            "abilityID": 72,
            "rank": 10
        },
        {
            "abilityID": 73,
            "rank": 10
        },
        {
            "abilityID": 74,
            "rank": 10
        },
        {
            "abilityID": 75,
            "rank": 10
        },
        {
            "abilityID": 76,
            "rank": 10
        },
        {
            "abilityID": 77,
            "rank": 10
        },
        {
            "abilityID": 88,
            "rank": 10
        },
        {
            "abilityID": 89,
            "rank": 10
        },
        {
            "abilityID": 90,
            "rank": 10
        },
        {
            "abilityID": 91,
            "rank": 10
        },
        {
            "abilityID": 92,
            "rank": 10
        },
        {
            "abilityID": 93,
            "rank": 10
        },
        {
            "abilityID": 94,
            "rank": 10
        },
        {
            "abilityID": 95,
            "rank": 10
        },
        {
            "abilityID": 96,
            "rank": 10
        },
        {
            "abilityID": 97,
            "rank": 10
        }
    ],
    "mage": [
        {
            "abilityID": 10,
            "rank": 10
        },
        {
            "abilityID": 11,
            "rank": 10
        },
        {
            "abilityID": 12,
            "rank": 10
        },
        {
            "abilityID": 13,
            "rank": 10
        },
        {
            "abilityID": 14,
            "rank": 10
        },
        {
            "abilityID": 15,
            "rank": 10
        },
        {
            "abilityID": 16,
            "rank": 10
        },
        {
            "abilityID": 17,
            "rank": 10
        },
        {
            "abilityID": 18,
            "rank": 10
        },
        {
            "abilityID": 28,
            "rank": 10
        },
        {
            "abilityID": 29,
            "rank": 10
        },
        {
            "abilityID": 30,
            "rank": 10
        },
        {
            "abilityID": 31,
            "rank": 10
        },
        {
            "abilityID": 32,
            "rank": 10
        },
        {
            "abilityID": 33,
            "rank": 10
        },
        {
            "abilityID": 34,
            "rank": 10
        },
        {
            "abilityID": 35,
            "rank": 10
        },
        {
            "abilityID": 36,
            "rank": 10
        },
        {
            "abilityID": 37,
            "rank": 10
        },
        {
            "abilityID": 58,
            "rank": 10
        },
        {
            "abilityID": 59,
            "rank": 10
        },
        {
            "abilityID": 60,
            "rank": 10
        },
        {
            "abilityID": 61,
            "rank": 10
        },
        {
            "abilityID": 62,
            "rank": 10
        },
        {
            "abilityID": 63,
            "rank": 10
        },
        {
            "abilityID": 64,
            "rank": 10
        },
        {
            "abilityID": 65,
            "rank": 10
        },
        {
            "abilityID": 66,
            "rank": 10
        },
        {
            "abilityID": 67,
            "rank": 10
        },
        {
            "abilityID": 98,
            "rank": 10
        },
        {
            "abilityID": 99,
            "rank": 10
        },
        {
            "abilityID": 100,
            "rank": 10
        },
        {
            "abilityID": 101,
            "rank": 10
        },
        {
            "abilityID": 102,
            "rank": 10
        },
        {
            "abilityID": 103,
            "rank": 10
        },
        {
            "abilityID": 104,
            "rank": 10
        },
        {
            "abilityID": 105,
            "rank": 10
        },
        {
            "abilityID": 106,
            "rank": 10
        },
        {
            "abilityID": 107,
            "rank": 10
        }

    ],
}