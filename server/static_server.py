# static_server.py
import threading
from http.server import <PERSON>TT<PERSON>erver, SimpleHTTPRequestHandler

def start_static_server(
    host: str = "127.0.0.1",
    port: int = 80,
    directory: str = "content/localhost"
):
    class _Handler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory=directory, **kwargs)

        def _send_ok(self):
            """Utility to respond with a short OK message."""
            response = b"OK"
            self.send_response(200)
            self.send_header("Content-Type", "text/plain")
            self.send_header("Content-Length", str(len(response)))
            self.send_header("Access-Control-Allow-Origin", "*")
            self.end_headers()
            if self.command != "HEAD":
                self.wfile.write(response)

        def do_POST(self):
            """Respond to POST requests from analytics endpoints."""
            # Read the request body to avoid connection issues
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                self.rfile.read(content_length)

            print(f"[Static] POST {self.path} - responding OK")
            self._send_ok()

        def do_HEAD(self):
            """Handle HEAD requests that some clients may issue."""
            self._send_ok()

        def do_OPTIONS(self):
            """Respond to preflight requests used by some runtimes."""
            self.send_response(204)
            self.send_header("Access-Control-Allow-Origin", "*")
            self.send_header("Access-Control-Allow-Methods", "POST, GET, HEAD, OPTIONS")
            self.send_header("Access-Control-Allow-Headers", "Content-Type")
            self.end_headers()

        def log_message(self, format, *args):
            """Override to reduce log spam, only log errors."""
            if "501" in str(args) or "error" in str(args).lower():
                super().log_message(format, *args)

    httpd = HTTPServer((host, port), _Handler)
    threading.Thread(target=httpd.serve_forever, daemon=True).start()
    print(f"[Static] Serving ./{directory} at http://{host}:{port}/")
    return httpd
