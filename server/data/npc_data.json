{"TutorialBoat": [{"id": 1, "name": "GreenKnightHard", "x": 6294.25, "y": 618.99, "z": 10.0, "team": 0, "entState": 0, "untargetable": false, "behavior_id": 1, "behavior_speed": 1.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 50, "power_id": 2, "facing_left": true, "health_delta": 1, "buffs": [], "max_hp": 0, "mount_id": 1, "buff_icon": 1, "is_player": false}], "NewbieRoad": [{"id": 1, "name": "GreenKnightHard", "x": 2971.200352619656, "y": 536.5181209587174, "z": 10.0, "team": 0, "entState": 0, "untargetable": false, "behavior_id": 1, "behavior_speed": 1.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 50, "power_id": 2, "facing_left": true, "health_delta": 1, "buffs": [], "max_hp": 0, "mount_id": 1, "buff_icon": 1, "is_player": false}], "CraftTown": [{"id": 1, "name": "IntroGoblinDagger", "x": 800.25, "y": 1400.615, "z": -10, "team": 0, "entState": 0, "untargetable": false, "behavior_id": 0, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": false, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}, {"id": 2, "name": "IntroGoblinDagger", "x": 800.25, "y": 1300.615, "z": -10, "team": 1, "entState": 1, "untargetable": false, "behavior_id": 1, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": true, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}, {"id": 3, "name": "IntroGoblinDagger", "x": 1000.25, "y": 1400.615, "z": -10, "team": 2, "entState": 2, "untargetable": false, "behavior_id": 2, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": false, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}, {"id": 4, "name": "IntroGoblinDagger", "x": 1100.25, "y": 1400.615, "z": -10, "team": 3, "entState": 3, "untargetable": false, "behavior_id": 3, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": true, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}, {"id": 5, "name": "IntroGoblinDagger", "x": 1200.25, "y": 1400.615, "z": -10, "team": 4, "entState": 4, "untargetable": false, "behavior_id": 4, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": true, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}, {"id": 6, "name": "IntroGoblinDagger", "x": 1300.25, "y": 1400.615, "z": -10, "team": 1, "entState": 0, "untargetable": false, "behavior_id": 1, "behavior_speed": 0.0, "level_str": "CraftTown", "var_1958": "testing", "var_1879": "the guard", "level": 0, "power_id": 0, "facing_left": true, "health_delta": 0, "buffs": [], "max_hp": 0, "mount_id": 0, "buff_icon": 0, "is_player": false}]}