package
{
   public class class_24
   {
      
      public static var var_1128:uint = 25;
      
      public static const const_779:Vector.<String> = new Vector.<String>();
      
      public static var var_2653:uint;
       
      
      public function class_24()
      {
         super();
      }
      
      public static function method_19(param1:String) : void
      {
      }
      
      public static function method_7(param1:String) : void
      {
         throw new Error(param1);
      }
      
      public static function method_680() : void
      {
         var_1128 = 25;
         const_779.length = 0;
         var_2653 = 0;
      }
   }
}
