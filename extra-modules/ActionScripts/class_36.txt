package
{
   import flash.geom.Point;
   
   public class class_36
   {
      
      public static const const_922:Point = new Point();
      
      public static const const_847:Point = new Point();
      
      public static const const_1310:Point = new Point();
      
      public static const const_1198:Point = new Point();
      
      public static const QUEUEPOWER_POINT2:Point = new Point();
      
      public static const const_747:Point = new Point();
      
      public static const GETTARGETPIERCE_POINT2:Point = new Point();
      
      public static const const_803:Point = new Point();
      
      public static const const_952:Point = new Point();
      
      public static const CAMERAZONETEST_POINT2:Point = new Point();
      
      public static const CAMERAZONETEST_POINT3:Point = new Point();
       
      
      public function class_36()
      {
         super();
      }
   }
}
