package
{
   import flash.geom.Rectangle;
   
   public class class_162
   {
       
      
      internal var var_2628:Number;
      
      internal var var_2829:Number;
      
      internal var var_2406:Rectangle;
      
      internal var var_1244:Vector.<class_160>;
      
      internal var var_2949:<PERSON><PERSON><PERSON> = false;
      
      internal var var_2740:<PERSON><PERSON><PERSON>;
      
      internal var var_1242:<PERSON><PERSON><PERSON>;
      
      internal var var_2965:int;
      
      internal var var_2989:<PERSON><PERSON><PERSON>;
      
      public function class_162()
      {
         super();
      }
      
      public function method_1695() : void
      {
         this.var_2406 = null;
         this.var_1244 = null;
      }
   }
}
