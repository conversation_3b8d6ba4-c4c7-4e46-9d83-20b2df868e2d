package
{
   import flash.geom.Rectangle;
   
   public class class_77
   {
       
      
      internal var dObj:a_Hotspot;
      
      internal var posX:Number;
      
      internal var posY:Number;
      
      internal var rect:Rectangle;
      
      public function class_77(param1:Number, param2:Number, param3:Rectangle, param4:a_Hotspot)
      {
         super();
         this.posX = param1;
         this.posY = param2;
         this.rect = param3;
         this.dObj = param4;
      }
      
      public function method_1050() : void
      {
         this.dObj = null;
         this.rect = null;
      }
   }
}
