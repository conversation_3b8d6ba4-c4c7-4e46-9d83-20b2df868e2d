package
{
   public class Soundscape
   {
       
      
      internal var name:String;
      
      internal var var_1805:uint;
      
      internal var loop:String;
      
      internal var loopVol:Number = 1;
      
      internal var intro:String;
      
      internal var introVol:Number = 1;
      
      internal var var_1451:Vector.<class_31>;
      
      public function Soundscape()
      {
         this.var_1451 = new Vector.<class_31>();
         super();
      }
   }
}
