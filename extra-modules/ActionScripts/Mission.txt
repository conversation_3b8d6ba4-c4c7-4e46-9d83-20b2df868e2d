package
{
   public class Mission
   {
      
      public static const const_213:uint = 0;
      
      public static const const_58:uint = 1;
      
      public static const const_72:uint = 2;
       
      
      internal var missionID:uint;
      
      internal var var_145:uint;
      
      internal var currCount:uint;
      
      internal var var_1745:uint;
      
      internal var var_588:uint;
      
      internal var var_2806:uint;
      
      public function Mission(param1:uint, param2:uint, param3:uint)
      {
         super();
         this.missionID = param1;
         this.var_145 = param2;
         this.currCount = param3;
      }
      
      public function method_736() : void
      {
      }
   }
}
