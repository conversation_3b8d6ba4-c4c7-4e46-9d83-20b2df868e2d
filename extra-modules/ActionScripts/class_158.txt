package
{
   import flash.geom.Point;
   
   public class class_158
   {
       
      
      internal var var_2730:uint;
      
      internal var var_2253:int;
      
      internal var var_2330:int;
      
      internal var var_2615:int;
      
      internal var var_2495:int;
      
      internal var var_1904:Point;
      
      internal var var_1903:Point;
      
      public function class_158(param1:uint, param2:int, param3:int, param4:int, param5:int, param6:Point = null, param7:Point = null)
      {
         super();
         this.var_2730 = param1;
         this.var_2253 = param2;
         this.var_2330 = param3;
         this.var_2615 = param4;
         this.var_2495 = param5;
         this.var_1904 = param6;
         this.var_1903 = param7;
      }
      
      public function method_2018() : void
      {
         this.var_1904 = null;
         this.var_1903 = null;
      }
   }
}
