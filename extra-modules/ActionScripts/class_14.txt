package
{
   import flash.utils.Dictionary;
   
   public class class_14
   {
      
      public static var entTypesXMLs:Array;
      
      public static var entTypes:Array;
      
      public static var powerTypes:Array;
      
      public static var gearTypes:Array;
      
      public static var buffTypes:Array;
      
      public static var var_767:Array;
      
      public static var var_629:Array;
      
      public static var magicTypes:Array;
      
      public static var var_464:Array;
      
      public static var var_224:Array;
      
      public static var var_238:Array;
      
      public static var var_2127:Array;
      
      public static var var_2248:Array;
      
      public static var var_2613:Array;
      
      public static var var_2647:Array;
      
      public static var var_2123:Array;
      
      public static var var_194:Array;
      
      public static var var_686:Array;
      
      public static var var_478:Array;
      
      public static var var_872:Array;
      
      public static var var_278:Array;
      
      public static var var_212:Array;
      
      public static var var_661:Array;
      
      public static var var_838:Array;
      
      public static var var_2176:Array;
      
      public static var var_1626:Array;
      
      public static var var_419:Array;
      
      public static var var_2297:Array;
      
      public static var var_2342:Array;
      
      public static var var_949:Array;
      
      public static var powerTypesDict:Dictionary;
      
      public static var gearTypesDict:Dictionary;
      
      public static var var_421:Dictionary;
      
      public static var var_2207:Dictionary;
      
      public static var var_1762:Dictionary;
      
      public static var var_842:Dictionary;
      
      public static var var_2352:Dictionary;
      
      public static var magicTypesDict:Dictionary;
      
      public static var var_362:Dictionary;
      
      public static var var_233:Dictionary;
      
      public static var var_2088:Dictionary;
      
      public static var buffTypesDict:Dictionary;
      
      public static var var_142:Dictionary;
      
      public static var var_2015:Dictionary;
      
      public static var var_42:Dictionary;
      
      public static var var_1391:Dictionary;
      
      public static var var_2322:Dictionary;
      
      public static var var_1921:Dictionary;
      
      public static var var_2634:Dictionary;
      
      public static var var_2600:Dictionary;
      
      public static var var_1729:Dictionary;
      
      public static var var_999:Dictionary;
      
      public static var var_430:Dictionary;
      
      public static var var_368:Dictionary;
      
      public static var var_704:Dictionary;
      
      public static var var_526:Dictionary;
      
      public static var var_274:Dictionary;
      
      public static var var_1770:Dictionary;
      
      public static var var_1371:Dictionary;
      
      public static var var_1101:Dictionary;
      
      public static var var_2236:Dictionary;
      
      public static var var_1951:Dictionary;
      
      public static var var_2148:Dictionary;
      
      public static var var_2027:Dictionary;
      
      public static var var_303:Dictionary;
      
      public static var var_1856:Dictionary;
      
      public static var var_2278:Dictionary;
      
      public static var var_1194:Dictionary;
       
      
      public function class_14()
      {
         super();
      }
      
      public static function method_1668() : void
      {
         entTypesXMLs = new Array();
         gearTypes = new Array();
         powerTypes = new Array();
         entTypes = new Array();
         buffTypes = new Array();
         var_767 = new Array();
         var_629 = new Array();
         magicTypes = new Array();
         var_464 = new Array();
         var_224 = new Array();
         var_238 = new Array();
         var_2127 = new Array();
         var_2248 = new Array();
         var_2613 = new Array();
         var_2647 = new Array();
         var_2123 = new Array();
         var_194 = new Array();
         var_686 = new Array();
         var_478 = new Array();
         var_872 = new Array();
         var_278 = new Array();
         var_212 = new Array();
         var_661 = new Array();
         var_838 = new Array();
         var_2176 = new Array();
         var_1626 = new Array();
         var_419 = new Array();
         var_2297 = new Array();
         var_2342 = new Array();
         var_949 = new Array();
         powerTypesDict = new Dictionary();
         gearTypesDict = new Dictionary();
         var_421 = new Dictionary();
         var_2207 = new Dictionary();
         var_1762 = new Dictionary();
         var_842 = new Dictionary();
         var_2352 = new Dictionary();
         var_142 = new Dictionary();
         var_2015 = new Dictionary();
         magicTypesDict = new Dictionary();
         var_362 = new Dictionary();
         var_233 = new Dictionary();
         var_2088 = new Dictionary();
         buffTypesDict = new Dictionary();
         var_42 = new Dictionary();
         var_1391 = new Dictionary();
         var_2322 = new Dictionary();
         var_1921 = new Dictionary();
         var_2634 = new Dictionary();
         var_2600 = new Dictionary();
         var_1729 = new Dictionary();
         var_999 = new Dictionary();
         var_430 = new Dictionary();
         var_368 = new Dictionary();
         var_704 = new Dictionary();
         var_526 = new Dictionary();
         var_274 = new Dictionary();
         var_1770 = new Dictionary();
         var_1371 = new Dictionary();
         var_1101 = new Dictionary();
         var_2148 = new Dictionary();
         var_2236 = new Dictionary();
         var_1951 = new Dictionary();
         var_2027 = new Dictionary();
         var_303 = new Dictionary();
         var_1856 = new Dictionary();
         var_2278 = new Dictionary();
         var_1194 = new Dictionary();
      }
   }
}
