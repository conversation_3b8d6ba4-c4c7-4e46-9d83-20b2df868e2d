package
{
   public class class_39
   {
       
      
      internal var mAbilityProperty:Object;
      
      internal var var_1388:String;
      
      public function class_39(param1:String)
      {
         super();
         this.mAbilityProperty = new Object();
         this.var_1388 = param1;
      }
      
      public function method_2133() : void
      {
         this.mAbilityProperty = null;
      }
      
      public function add(param1:String, param2:Array) : void
      {
         this.mAbilityProperty[param1] = param2;
      }
   }
}
