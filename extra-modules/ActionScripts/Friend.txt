package
{
   public class Friend
   {
       
      
      internal var charName:String;
      
      internal var var_207:String;
      
      internal var bOnline:Boolean;
      
      internal var var_276:Boolean;
      
      internal var var_2100:uint;
      
      internal var className:String;
      
      internal var var_2659:String;
      
      internal var var_289:uint;
      
      public function Friend(param1:String = null, param2:String = null, param3:Boolean = false, param4:uint = 0, param5:String = null, param6:Boolean = false, param7:uint = 0)
      {
         super();
         this.charName = param1;
         this.bOnline = param3;
         this.var_2100 = param4;
         this.className = param5;
         this.var_207 = param2;
         this.var_276 = param6;
         this.var_289 = param7;
      }
   }
}
