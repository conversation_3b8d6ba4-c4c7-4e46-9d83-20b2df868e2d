package
{
   public class class_160
   {
       
      
      internal var var_2343:Number;
      
      internal var var_2423:Number;
      
      public var mRateX:Number;
      
      public var var_2640:Number;
      
      internal var var_2260:Number;
      
      internal var var_2433:Number;
      
      internal var var_954:MagicObject;
      
      public function class_160(param1:Number, param2:Number, param3:Number, param4:Number)
      {
         super();
         this.var_2343 = param1;
         this.var_2423 = param2;
         this.mRateX = param3;
         this.var_2640 = param4;
         this.var_2260 = 0;
         this.var_2433 = 0;
      }
      
      public function method_1263() : void
      {
         this.var_954 = null;
      }
   }
}
