package
{
   public class class_151
   {
       
      
      internal var var_286:class_15;
      
      internal var stackCount:uint;
      
      public function class_151(param1:class_15, param2:uint)
      {
         super();
         this.stackCount = param2;
         this.var_286 = param1;
      }
      
      public function method_777() : void
      {
         this.var_286 = null;
      }
   }
}
