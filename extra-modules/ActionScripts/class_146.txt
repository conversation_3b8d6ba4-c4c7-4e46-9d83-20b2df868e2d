package
{
   public class class_146
   {
       
      
      internal var skit:Array = null;
      
      internal var var_1500:Number = 0;
      
      internal var bLocal:Boolean = false;
      
      internal var var_2412:Boolean;
      
      internal var targetName:String;
      
      internal var targetClass:String;
      
      public function class_146(param1:Boolean)
      {
         super();
         this.bLocal = param1;
      }
      
      public function method_834() : void
      {
         this.skit = null;
      }
   }
}
